'use client'
import AppCenterTitle from '@/app/home/<USER>/components/Title'
import AppCenterSearch from '@/app/home/<USER>/components/search'
import { COMMON } from '@/app/home/<USER>'
import { useState } from 'react'
export default function AppCenterPage() {
  const [selectBtn, setSelectBtn] = useState(0)
  return (
    <>
      <div className="size-full px-[300px] py-[75px]">
        {/* 标题 */}
        <div className="flex items-start pb-[50px]">
          <AppCenterTitle />
        </div>
        {/* 分类按钮及搜索 */}
          <AppCenterSearch selectItems={COMMON.appCenterSelectItems} selectBtn={selectBtn} setSelectBtn={setSelectBtn} />
        {/* 应用选择 */}
      </div>
    </>
  )
}
