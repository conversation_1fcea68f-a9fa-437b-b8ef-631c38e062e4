import { Button, Input } from 'antd'
import { SearchOutlined } from '@ant-design/icons'
export default function AppCenterSearch({ selectItems, selectBtn, setSelectBtn }: {
  selectItems: any,
  selectBtn: number,
  setSelectBtn: any
}) {
  return (
    <div className="flex justify-between font-[PingFangSC]">
      {/* 按钮 */}
      <div className="flex gap-[13px] text-[14px] leading-[20px] font-[500]">
        {
          selectItems.map((item: any, index: number) => {
            return <Button key={index} color="default" variant={selectBtn === index ? 'solid' : 'outlined'} onClick={() => setSelectBtn(index)}>{item.name}</Button>
          })
        }
      </div>
      {/* 搜索 */}
      <div>
        <Input placeholder="default size" prefix={<SearchOutlined />} />
      </div>
    </div>
  )
}
